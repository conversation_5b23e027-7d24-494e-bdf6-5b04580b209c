'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Presentation, Slide } from '@/types';
import { ChevronLeft, ChevronRight, Save, Eye } from 'lucide-react';
import { SlidePreview } from './slide-preview';

interface PresentationEditorProps {
  presentationId: string;
}

export function PresentationEditor({ presentationId }: PresentationEditorProps) {
  const [presentation, setPresentation] = useState<Presentation | null>(null);
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string>('');
  const [saveSuccess, setSaveSuccess] = useState(false);

  // Load presentation
  useEffect(() => {
    loadPresentation();
  }, [presentationId]);

  const loadPresentation = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/presentations/${presentationId}`);
      const data = await response.json();

      if (data.success) {
        setPresentation(data.data);
      } else {
        setError(data.error || 'Failed to load presentation');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const savePresentation = async () => {
    if (!presentation) return;

    try {
      setIsSaving(true);
      const response = await fetch(`/api/presentations/${presentationId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(presentation),
      });

      const data = await response.json();
      if (data.success) {
        setSaveSuccess(true);
        setError('');
        // Clear success message after 2 seconds
        setTimeout(() => setSaveSuccess(false), 2000);
      } else {
        setError(data.error || 'Failed to save presentation');
        setSaveSuccess(false);
      }
    } catch (err) {
      setError('Failed to save presentation');
      setSaveSuccess(false);
    } finally {
      setIsSaving(false);
    }
  };

  const updateSlide = (slideIndex: number, updatedSlide: Partial<Slide>) => {
    if (!presentation) return;

    // Clear any previous errors/success when user starts editing
    setError('');
    setSaveSuccess(false);

    const updatedSlides = presentation.slides.map((slide, index) =>
      index === slideIndex ? { ...slide, ...updatedSlide } : slide
    );

    setPresentation({
      ...presentation,
      slides: updatedSlides,
    });
  };

  const updatePresentationTitle = (title: string) => {
    if (!presentation) return;

    // Clear any previous errors/success when user starts editing
    setError('');
    setSaveSuccess(false);

    setPresentation({ ...presentation, title });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading presentation...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="max-w-md">
          <CardContent className="pt-6">
            <p className="text-red-600 text-center">{error}</p>
            <Button onClick={loadPresentation} className="w-full mt-4">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!presentation) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>Presentation not found</p>
      </div>
    );
  }

  const currentSlide = presentation.slides[currentSlideIndex];

  return (
    <div className="flex h-screen">
      {/* Sidebar - Slide Navigation */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        <div className="p-4 border-b">
          <Input
            value={presentation.title}
            onChange={(e) => updatePresentationTitle(e.target.value)}
            className="font-semibold text-lg"
            placeholder="Presentation Title"
          />
        </div>

        <div className="flex-1 overflow-y-auto p-4 space-y-2">
          {presentation.slides.map((slide, index) => (
            <Card
              key={slide._id}
              className={`cursor-pointer transition-colors ${
                index === currentSlideIndex
                  ? 'ring-2 ring-blue-500 bg-blue-50'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => setCurrentSlideIndex(index)}
            >
              <CardContent className="p-3">
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-xs font-medium text-gray-500">
                    Slide {index + 1}
                  </span>
                  <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                    {slide.type}
                  </span>
                </div>
                <h4 className="font-medium text-sm truncate">{slide.title}</h4>
                <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                  {slide.content.substring(0, 60)}...
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="p-4 border-t space-y-2">
          {error && (
            <p className="text-sm text-red-600 text-center">{error}</p>
          )}
          {saveSuccess && (
            <p className="text-sm text-green-600 text-center">✓ Saved successfully!</p>
          )}
          <Button
            onClick={savePresentation}
            disabled={isSaving}
            className="w-full"
            variant={saveSuccess ? "outline" : "default"}
          >
            <Save className="w-4 h-4 mr-2" />
            {isSaving ? 'Saving...' : saveSuccess ? 'Saved!' : 'Save Changes'}
          </Button>
        </div>
      </div>

      {/* Main Editor */}
      <div className="flex-1 flex flex-col">
        {/* Toolbar */}
        <div className="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentSlideIndex(Math.max(0, currentSlideIndex - 1))}
              disabled={currentSlideIndex === 0}
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
            <span className="text-sm text-gray-600">
              {currentSlideIndex + 1} of {presentation.slides.length}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setCurrentSlideIndex(Math.min(presentation.slides.length - 1, currentSlideIndex + 1))
              }
              disabled={currentSlideIndex === presentation.slides.length - 1}
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>

          <Button variant="outline" size="sm">
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </Button>
        </div>

        {/* Slide Editor */}
        <div className="flex-1 p-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Editor Panel */}
          {currentSlide && (
            <Card className="h-fit">
              <CardHeader>
                <CardTitle className="text-lg">Edit Slide</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    Slide Title
                  </label>
                  <Input
                    value={currentSlide.title}
                    onChange={(e) =>
                      updateSlide(currentSlideIndex, { title: e.target.value })
                    }
                    placeholder="Slide Title"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    Slide Content
                  </label>
                  <Textarea
                    value={currentSlide.content}
                    onChange={(e) =>
                      updateSlide(currentSlideIndex, { content: e.target.value })
                    }
                    className="min-h-[300px] resize-none"
                    placeholder="Slide content..."
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Preview Panel */}
          {currentSlide && (
            <div>
              <h3 className="text-lg font-medium text-gray-700 mb-4">Preview</h3>
              <SlidePreview
                slide={currentSlide}
                template={presentation?.template || 'modern'}
                className="w-full max-w-2xl"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
