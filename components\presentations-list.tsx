'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Presentation } from '@/types';
import { Edit, Plus, Calendar, FileText } from 'lucide-react';
import Link from 'next/link';

export function PresentationsList() {
  const router = useRouter();
  const [presentations, setPresentations] = useState<Presentation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    loadPresentations();
  }, []);

  const loadPresentations = async () => {
    try {
      setIsLoading(true);
      // For now, using demo-user. In Phase 7, we'll use actual user authentication
      const response = await fetch('/api/presentations?userId=demo-user');
      const data = await response.json();

      if (data.success) {
        setPresentations(data.data || []);
      } else {
        setError(data.error || 'Failed to load presentations');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading presentations...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="max-w-md mx-auto">
        <CardContent className="pt-6 text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={loadPresentations}>Try Again</Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Create New Button */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            {presentations.length} Presentation{presentations.length !== 1 ? 's' : ''}
          </h2>
        </div>
        <Link href="/demo">
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Create New
          </Button>
        </Link>
      </div>

      {/* Presentations Grid */}
      {presentations.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No presentations yet
            </h3>
            <p className="text-gray-600 mb-4">
              Create your first AI-powered presentation to get started
            </p>
            <Link href="/demo">
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Presentation
              </Button>
            </Link>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {presentations.map((presentation) => (
            <Card key={presentation._id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="line-clamp-2">{presentation.title}</CardTitle>
                <CardDescription className="line-clamp-1">
                  Topic: {presentation.topic}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <FileText className="w-4 h-4 mr-2" />
                    {presentation.slides.length} slides
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Calendar className="w-4 h-4 mr-2" />
                    {formatDate(presentation.metadata.createdAt.toString())}
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <span className="inline-block w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                    {presentation.template} template
                  </div>
                  <div className="pt-2">
                    <Button 
                      onClick={() => router.push(`/presentation/${presentation._id}`)}
                      className="w-full"
                      size="sm"
                    >
                      <Edit className="w-4 h-4 mr-2" />
                      Edit Presentation
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
