'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Sparkles } from 'lucide-react';

interface GenerationForm {
  topic: string;
  audience: string;
  slideCount: number;
  tone: string;
  template: string;
}

export function PresentationGenerator() {
  const router = useRouter();
  const [form, setForm] = useState<GenerationForm>({
    topic: '',
    audience: 'general',
    slideCount: 8,
    tone: 'professional',
    template: 'modern'
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string>('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!form.topic.trim()) {
      setError('Please enter a topic for your presentation');
      return;
    }

    setIsGenerating(true);
    setError('');
    setResult(null);

    try {
      const response = await fetch('/api/presentations/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: 'demo-user', // For now, using a demo user ID
          ...form
        }),
      });

      const data = await response.json();

      if (data.success) {
        setResult(data.data);
        // Redirect to editor after a short delay
        setTimeout(() => {
          router.push(`/presentation/${data.data._id}`);
        }, 2000);
      } else {
        setError(data.error || 'Failed to generate presentation');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-6 w-6 text-blue-600" />
            AI Presentation Generator
          </CardTitle>
          <CardDescription>
            Describe your topic and let AI create a professional presentation for you
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="topic">Presentation Topic *</Label>
              <Textarea
                id="topic"
                placeholder="e.g., Introduction to Machine Learning, Marketing Strategy for 2024, Climate Change Solutions..."
                value={form.topic}
                onChange={(e) => setForm({ ...form, topic: e.target.value })}
                className="min-h-[100px]"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="audience">Target Audience</Label>
                <Select value={form.audience} onValueChange={(value) => setForm({ ...form, audience: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General Audience</SelectItem>
                    <SelectItem value="business">Business Professionals</SelectItem>
                    <SelectItem value="academic">Academic/Educational</SelectItem>
                    <SelectItem value="technical">Technical Experts</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="tone">Presentation Tone</Label>
                <Select value={form.tone} onValueChange={(value) => setForm({ ...form, tone: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="professional">Professional</SelectItem>
                    <SelectItem value="casual">Casual</SelectItem>
                    <SelectItem value="academic">Academic</SelectItem>
                    <SelectItem value="creative">Creative</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="slideCount">Number of Slides</Label>
                <Input
                  id="slideCount"
                  type="number"
                  min="3"
                  max="50"
                  value={form.slideCount}
                  onChange={(e) => setForm({ ...form, slideCount: parseInt(e.target.value) || 8 })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="template">Template</Label>
                <Select value={form.template} onValueChange={(value) => setForm({ ...form, template: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="modern">Modern</SelectItem>
                    <SelectItem value="minimal">Minimal</SelectItem>
                    <SelectItem value="creative">Creative</SelectItem>
                    <SelectItem value="academic">Academic</SelectItem>
                    <SelectItem value="corporate">Corporate</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Button 
              type="submit" 
              className="w-full" 
              disabled={isGenerating || !form.topic.trim()}
              size="lg"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating Presentation...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  Generate Presentation
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <p className="text-red-600">{error}</p>
          </CardContent>
        </Card>
      )}

      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="text-green-600">✅ Presentation Generated!</CardTitle>
            <CardDescription>
              "{result.title}" - {result.slides.length} slides - Generated in {result.metadata.generationTime}ms
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Slides Preview:</h4>
                <div className="space-y-2">
                  {result.slides.map((slide: any, index: number) => (
                    <div key={slide._id} className="p-3 border rounded-lg">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-sm font-medium text-gray-500">Slide {index + 1}</span>
                        <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">{slide.type}</span>
                      </div>
                      <h5 className="font-medium">{slide.title}</h5>
                      <p className="text-sm text-gray-600 mt-1">{slide.content.substring(0, 100)}...</p>
                    </div>
                  ))}
                </div>
              </div>
              <div className="pt-4 border-t flex items-center justify-between">
                <p className="text-sm text-gray-600">
                  Presentation ID: <code className="bg-gray-100 px-2 py-1 rounded">{result._id}</code>
                </p>
                <Button onClick={() => router.push(`/presentation/${result._id}`)}>
                  Edit Presentation
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
