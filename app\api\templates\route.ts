import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse } from '@/types';

interface Template {
  id: string;
  name: string;
  description: string;
  preview: string;
  category: 'business' | 'academic' | 'creative' | 'minimal';
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
  styles: {
    titleFont: string;
    contentFont: string;
    titleSize: string;
    contentSize: string;
  };
}

// Static template data - in a real app, this might come from a database
const templates: Template[] = [
  {
    id: 'modern',
    name: 'Modern',
    description: 'Clean and professional design with bold typography',
    preview: '/templates/modern-preview.jpg',
    category: 'business',
    colors: {
      primary: '#2563eb',
      secondary: '#64748b',
      accent: '#0ea5e9',
      background: '#ffffff',
      text: '#1e293b'
    },
    styles: {
      titleFont: 'font-bold',
      contentFont: 'font-normal',
      titleSize: 'text-3xl',
      contentSize: 'text-base'
    }
  },
  {
    id: 'minimal',
    name: 'Mini<PERSON>',
    description: 'Simple and elegant with plenty of white space',
    preview: '/templates/minimal-preview.jpg',
    category: 'minimal',
    colors: {
      primary: '#000000',
      secondary: '#6b7280',
      accent: '#374151',
      background: '#ffffff',
      text: '#111827'
    },
    styles: {
      titleFont: 'font-light',
      contentFont: 'font-light',
      titleSize: 'text-2xl',
      contentSize: 'text-sm'
    }
  },
  {
    id: 'creative',
    name: 'Creative',
    description: 'Vibrant and dynamic design for creative presentations',
    preview: '/templates/creative-preview.jpg',
    category: 'creative',
    colors: {
      primary: '#7c3aed',
      secondary: '#ec4899',
      accent: '#f59e0b',
      background: '#fafafa',
      text: '#1f2937'
    },
    styles: {
      titleFont: 'font-extrabold',
      contentFont: 'font-medium',
      titleSize: 'text-4xl',
      contentSize: 'text-lg'
    }
  },
  {
    id: 'academic',
    name: 'Academic',
    description: 'Traditional and scholarly design for educational content',
    preview: '/templates/academic-preview.jpg',
    category: 'academic',
    colors: {
      primary: '#1f2937',
      secondary: '#4b5563',
      accent: '#059669',
      background: '#ffffff',
      text: '#374151'
    },
    styles: {
      titleFont: 'font-semibold',
      contentFont: 'font-normal',
      titleSize: 'text-2xl',
      contentSize: 'text-base'
    }
  },
  {
    id: 'corporate',
    name: 'Corporate',
    description: 'Professional design suitable for business presentations',
    preview: '/templates/corporate-preview.jpg',
    category: 'business',
    colors: {
      primary: '#1e40af',
      secondary: '#475569',
      accent: '#dc2626',
      background: '#f8fafc',
      text: '#0f172a'
    },
    styles: {
      titleFont: 'font-bold',
      contentFont: 'font-normal',
      titleSize: 'text-3xl',
      contentSize: 'text-base'
    }
  }
];

// GET /api/templates - Get all available templates
export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<Template[]>>> {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    
    let filteredTemplates = templates;
    
    if (category && category !== 'all') {
      filteredTemplates = templates.filter(template => template.category === category);
    }

    return NextResponse.json({
      success: true,
      data: filteredTemplates
    });
  } catch (error) {
    console.error('Error fetching templates:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch templates'
    }, { status: 500 });
  }
}
