'use client';

import { Slide } from '@/types';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

interface SlidePreviewProps {
  slide: Slide;
  template?: string;
  className?: string;
}

export function SlidePreview({ slide, template = 'modern', className = '' }: SlidePreviewProps) {
  const getTemplateStyles = (templateId: string) => {
    switch (templateId) {
      case 'minimal':
        return {
          background: 'bg-white',
          titleClass: 'text-2xl font-light text-black',
          contentClass: 'text-sm font-light text-gray-700',
          accent: 'border-l-4 border-gray-400'
        };
      case 'creative':
        return {
          background: 'bg-gradient-to-br from-purple-50 to-pink-50',
          titleClass: 'text-4xl font-extrabold text-purple-700',
          contentClass: 'text-lg font-medium text-gray-800',
          accent: 'border-l-4 border-purple-500'
        };
      case 'academic':
        return {
          background: 'bg-white',
          titleClass: 'text-2xl font-semibold text-gray-800',
          contentClass: 'text-base font-normal text-gray-700',
          accent: 'border-l-4 border-green-600'
        };
      case 'corporate':
        return {
          background: 'bg-slate-50',
          titleClass: 'text-3xl font-bold text-blue-800',
          contentClass: 'text-base font-normal text-slate-700',
          accent: 'border-l-4 border-blue-600'
        };
      default: // modern
        return {
          background: 'bg-white',
          titleClass: 'text-3xl font-bold text-blue-600',
          contentClass: 'text-base font-normal text-slate-700',
          accent: 'border-l-4 border-blue-500'
        };
    }
  };

  const styles = getTemplateStyles(template);

  const formatContent = (content: string) => {
    // Simple formatting for bullet points and line breaks
    return content
      .split('\n')
      .map((line, index) => {
        if (line.trim().startsWith('•') || line.trim().startsWith('-')) {
          return (
            <li key={index} className="ml-4">
              {line.replace(/^[•-]\s*/, '')}
            </li>
          );
        }
        if (line.trim().startsWith('**') && line.trim().endsWith('**')) {
          return (
            <p key={index} className="font-semibold mt-2">
              {line.replace(/\*\*/g, '')}
            </p>
          );
        }
        return line.trim() ? (
          <p key={index} className="mt-2">
            {line}
          </p>
        ) : null;
      })
      .filter(Boolean);
  };

  return (
    <Card className={`${styles.background} ${className} aspect-[16/9] flex flex-col`}>
      {slide.type === 'title' ? (
        <CardContent className="flex-1 flex flex-col items-center justify-center text-center p-8">
          <h1 className={`${styles.titleClass} mb-4`}>{slide.title}</h1>
          <div className={styles.contentClass}>
            {formatContent(slide.content)}
          </div>
        </CardContent>
      ) : (
        <>
          <CardHeader className={`${styles.accent} pl-6`}>
            <h2 className={styles.titleClass}>{slide.title}</h2>
          </CardHeader>
          <CardContent className="flex-1 p-6">
            <div className={styles.contentClass}>
              {slide.content.includes('•') || slide.content.includes('-') ? (
                <ul className="space-y-1">{formatContent(slide.content)}</ul>
              ) : (
                <div className="space-y-2">{formatContent(slide.content)}</div>
              )}
            </div>
          </CardContent>
        </>
      )}
    </Card>
  );
}
