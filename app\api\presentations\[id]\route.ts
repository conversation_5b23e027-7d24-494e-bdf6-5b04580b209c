import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Presentation from '@/models/Presentation';
import { ApiResponse, Presentation as PresentationType } from '@/types';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// GET /api/presentations/[id] - Get a specific presentation
export async function GET(
  request: NextRequest,
  { params }: RouteParams
): Promise<NextResponse<ApiResponse<PresentationType>>> {
  try {
    await connectDB();

    const { id } = await params;
    const presentation = await Presentation.findById(id).lean();
    
    if (!presentation) {
      return NextResponse.json({
        success: false,
        error: 'Presentation not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: {
        ...presentation,
        _id: presentation._id.toString()
      }
    });
  } catch (error) {
    console.error('Error fetching presentation:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch presentation'
    }, { status: 500 });
  }
}

// PUT /api/presentations/[id] - Update a presentation
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
): Promise<NextResponse<ApiResponse<PresentationType>>> {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();
    const updateData = {
      ...body,
      'metadata.updatedAt': new Date()
    };

    const presentation = await Presentation.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).lean();

    if (!presentation) {
      return NextResponse.json({
        success: false,
        error: 'Presentation not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: {
        ...presentation,
        _id: presentation._id.toString()
      }
    });
  } catch (error) {
    console.error('Error updating presentation:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update presentation'
    }, { status: 500 });
  }
}

// DELETE /api/presentations/[id] - Delete a presentation
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
): Promise<NextResponse<ApiResponse<null>>> {
  try {
    await connectDB();

    const { id } = await params;
    const presentation = await Presentation.findByIdAndDelete(id);
    
    if (!presentation) {
      return NextResponse.json({
        success: false,
        error: 'Presentation not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: 'Presentation deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting presentation:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to delete presentation'
    }, { status: 500 });
  }
}
